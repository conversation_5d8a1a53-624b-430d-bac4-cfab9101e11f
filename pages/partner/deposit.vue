<template>
  <view class="min-h-screen bg-gray-50 p-4">
    <!-- 页面标题 -->
    <view class="text-center mb-6">
      <text class="text-xl font-bold text-gray-800">押金变动记录</text>
      <text class="text-sm text-gray-500 block mt-1">查看您的押金账户余额变动明细</text>
    </view>

    <!-- 空状态 -->
    <view v-if="list.length === 0" class="flex flex-col items-center justify-center py-20">
      <view class="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mb-4">
        <text class="text-3xl text-gray-400">💰</text>
      </view>
      <text class="text-gray-500 text-base mb-2">暂无押金变动记录</text>
      <text class="text-gray-400 text-sm">您的押金变动记录将在这里显示</text>
    </view>

    <!-- 押金变动列表 -->
    <view v-else class="space-y-3">
      <view
        v-for="(item, index) in list"
        :key="index"
        class="bg-white rounded-lg shadow-sm p-4 border border-gray-100 hover:shadow-md transition-shadow"
      >
        <!-- 顶部信息行 -->
        <view class="flex items-center justify-between mb-3">
          <!-- 左侧类型信息 -->
          <view class="flex items-center">
            <view class="w-10 h-10 rounded-full flex items-center justify-center mr-3" :class="getTypeIconBgClass(item.type)">
              <text class="text-white text-lg">{{ getTypeIcon(item.type) }}</text>
            </view>
            <view>
              <text class="text-base font-medium text-gray-800">{{ getTypeText(item.type) }}</text>
              <text class="text-xs text-gray-500 block">{{ formatDate(item.createTime) }}</text>
            </view>
          </view>

          <!-- 右侧变动金额 -->
          <view class="text-right">
            <text class="text-lg font-bold" :class="getAmountClass(item.changeAmount)">
              {{ formatAmount(item.changeAmount) }}
            </text>
          </view>
        </view>

        <!-- 金额详情 -->
        <view class="bg-gray-50 rounded-lg p-3 space-y-2">
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">变动前余额</text>
            <text class="text-sm font-medium text-gray-800">¥{{ formatMoney(item.beforeAmount) }}</text>
          </view>
          <view class="flex justify-between items-center">
            <text class="text-sm text-gray-600">变动金额</text>
            <text class="text-sm font-medium" :class="getAmountClass(item.changeAmount)">
              {{ formatAmount(item.changeAmount) }}
            </text>
          </view>
          <view class="flex justify-between items-center border-t border-gray-200 pt-2">
            <text class="text-sm font-medium text-gray-700">变动后余额</text>
            <text class="text-sm font-bold text-gray-800">¥{{ formatMoney(item.afterAmount) }}</text>
          </view>
        </view>

        <!-- 底部状态指示 -->
        <view class="flex items-center pt-2 border-t border-gray-100 mt-3">
          <view class="w-2 h-2 rounded-full mr-2" :class="getStatusDotClass(item.type)"></view>
          <text class="text-xs text-gray-400">{{ getTypeDescription(item.type) }}</text>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view v-if="list.length > 0" class="text-center py-6">
      <text class="text-gray-400 text-sm">已显示全部 {{ list.length }} 条押金变动记录</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerDepositLogListData} from "@/common/api/partner"
interface Item{
  beforeAmount: number; // 变动前金额
  changeAmount: number; // 变动金额
  afterAmount: number; // 变动后金额
  type: number; // 类型:1=主动缴纳,2=提成抵扣,3=退还
  createTime: string; // 发生时间
}
const list = ref<Item[]>([])

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 格式化金额显示
const formatMoney = (amount: number): string => {
  return amount.toFixed(2)
}

// 格式化变动金额（带正负号）
const formatAmount = (amount: number): string => {
  if (amount > 0) {
    return `+¥${amount.toFixed(2)}`
  } else if (amount < 0) {
    return `-¥${Math.abs(amount).toFixed(2)}`
  } else {
    return `¥${amount.toFixed(2)}`
  }
}

// 获取类型文本
const getTypeText = (type: number): string => {
  switch (type) {
    case 1:
      return '主动缴纳'
    case 2:
      return '提成抵扣'
    case 3:
      return '押金退还'
    default:
      return '未知类型'
  }
}

// 获取类型描述
const getTypeDescription = (type: number): string => {
  switch (type) {
    case 1:
      return '您主动缴纳了押金'
    case 2:
      return '使用押金抵扣提成'
    case 3:
      return '押金已退还到您的账户'
    default:
      return '押金变动'
  }
}

// 获取类型图标
const getTypeIcon = (type: number): string => {
  switch (type) {
    case 1:
      return '💳'
    case 2:
      return '💸'
    case 3:
      return '💰'
    default:
      return '💱'
  }
}

// 获取类型图标背景样式
const getTypeIconBgClass = (type: number): string => {
  switch (type) {
    case 1:
      return 'bg-green-500'
    case 2:
      return 'bg-orange-500'
    case 3:
      return 'bg-blue-500'
    default:
      return 'bg-gray-500'
  }
}

// 获取金额样式类
const getAmountClass = (amount: number): string => {
  if (amount > 0) {
    return 'text-green-600'
  } else if (amount < 0) {
    return 'text-red-600'
  } else {
    return 'text-gray-600'
  }
}

// 获取状态指示点样式类
const getStatusDotClass = (type: number): string => {
  switch (type) {
    case 1:
      return 'bg-green-400'
    case 2:
      return 'bg-orange-400'
    case 3:
      return 'bg-blue-400'
    default:
      return 'bg-gray-400'
  }
}

const init = async () => {
  try {
    list.value = await getPartnerDepositLogListData()
  } catch (error) {
    console.error('获取押金记录列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

onShow(() => {
  init()
})


</script>


<style scoped lang="scss">

</style>
