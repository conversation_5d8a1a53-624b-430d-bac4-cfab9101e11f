<template>
  <view class="box-sizing-b w-full">
    <!-- 01. 头部组件 -->
    <use-header search-tip="请输入搜索关键字" :search-auto="true"></use-header>

    <!-- 02. 合伙人信息区 -->
    <view v-if="partnerInfo && partnerInfo.name" class="partner-info-area bg-white mx-4 mt-4 rounded-2xl shadow-sm border border-gray-100">
      <view class="p-6">
        <!-- 合伙人欢迎信息 -->
        <view class="flex items-center justify-between mb-4">
          <view class="flex items-center">
            <view class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
              <text class="text-white text-lg font-bold">{{ partnerInfo.name.charAt(0) }}</text>
            </view>
            <view>
              <text class="text-lg font-semibold text-gray-800">尊敬的合伙人{{ partnerInfo.name }}</text>
              <view class="flex items-center mt-1">
                <view class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full mr-2">
                  {{ getPartnerLevelName(partnerInfo.level) }}
                </view>
                <text class="text-xs text-gray-500">{{ formatDate(partnerInfo.proxyStartDate) }}加入</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 合伙人详细信息 -->
        <view class="grid grid-cols-2 gap-4 text-sm">
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">代理区域</text>
            <text class="font-medium text-gray-800">{{ partnerInfo.proxyArea || '全国' }}</text>
          </view>
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">提成权限</text>
            <text class="font-medium text-gray-800">{{ partnerInfo.commission || '待设置' }}</text>
          </view>
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">押金余额</text>
            <text class="font-medium text-gray-800">¥{{ partnerInfo.depositAmount || 0 }}</text>
          </view>
          <view class="bg-gray-50 p-3 rounded-lg">
            <text class="text-gray-600 block mb-1">押金状态</text>
            <text class="font-medium" :class="getDepositStatusClass(partnerInfo.depositStatus)">
              {{ getDepositStatusText(partnerInfo.depositStatus) }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 03. 轮播区 -->
    <view class="swiper-area pos-r" v-if="swiperDatas && swiperDatas.length > 0">
      <!-- 轮播组件 -->
      <swiper class="swiper w-full" autoplay indicator-dots indicator-color="#f7f7f7" indicator-active-color="#428675">
        <swiper-item class="swiper-item padding-lr wh-full box-sizing-b" v-for="(item, index) in swiperDatas"
                     :key="index">
          <view class="wh-full" @click.stop="topage(item)">
            <image class="border-radius wh-full" mode="aspectFill" :lazy-load="true" :src="item.image"/>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 03. 分类区 -->
    <view class="category-area dflex dflex-wrap-w" v-if="categoryDatas && categoryDatas.length > 0">
      <view class="category-item dflex dflex-flow-c margin-bottom-sm" v-for="(item, index) in categoryDatas"
            :key="index" @click="toCategory(item.id)">
        <image class="margin-bottom-xs" lazy-load :src="item.image"></image>
        <text class="tac clamp">{{ item.name }}</text>
      </view>
    </view>
    <view class="gap"></view>

    <!-- 04. 限时精选 -->
    <use-list-title title="限时精选" size="32" fwt="600" color="#333" iconfont="icondaishouhuo-" >
    </use-list-title>
    <view class="limit-area bg-white">
      <scroll-view class="padding-lr" scroll-x>
        <view class="dflex padding-bottom">
          <view class="item margin-right-sm" v-for="(item, index) in goodsLimitDatas" :key="index"
                @click="toGoods(item.id)">
            <image class="border-radius-xs" mode="aspectFill" :lazy-load="true" :src="item.image"></image>
            <text class="title clamp padding-bottom-xs">{{ item.name }}</text>
            <text class="price">{{ item.actualPrice }}</text>
            <text class="m-price">{{ item.originalPrice }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="gap"></view>

    <!-- 05. 热门推荐 -->
    <use-hot-goods :datas="goodsHotDatas" autoload="none" title="热门推荐"></use-hot-goods>

    <!-- 置顶 -->
    <use-totop ref="useTop" :style="{ marginBottom: '50px' }"></use-totop>


  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onLoad, onPageScroll, onShow, onPullDownRefresh} from '@dcloudio/uni-app'
import {getBannerListData, getCategoryListData} from "@/common/api"
import {getRecommendedListData} from "@/common/api/goods"
import {getPartnerInfoData} from "@/common/api/partner"
import {useWxJs} from "@/common/utils";

// 合伙人接口定义
interface Partner {
  name: string; //合伙人姓名，尊敬的合伙人xxx
  level: string; // 合伙人等级
  proxyStartDate: string; // 合伙人开始时间 yyyy-MM-dd HH:mm:ss
  depositAmount: number; // 押金余额
  proxyArea: string; // 代理区域
  commission: string; // 等级提成权限
  depositStatus: string; //押金状态:0=未缴纳,1=部分缴纳,2=完全缴纳,3=已退还
}

const {share} = useWxJs()
const swiperDatas = ref([])
const categoryDatas = ref([])
const goodsLimitDatas = ref([])
const goodsHotDatas = ref([])
const partnerInfo = ref<Partner | null>(null)

const useTop = ref(null)

// 合伙人级别配置
const partnerLevels = ref([
  {
    id: '1',
    name: '资源引荐官',
    deposit: 0,
    commission: '最高15%（一次性）',
    features: ['无门槛', '一次性分成', '仅对接资源']
  },
  {
    id: '2',
    name: 'T1级超级合伙人',
    deposit: 5000,
    commission: '9%（持续）',
    features: ['5000元押金', '持续性分成', '兼职拓展']
  },
  {
    id: '3',
    name: 'T2级超级合伙人',
    deposit: 10000,
    commission: '12%（持续）',
    features: ['10000元押金', '持续性分成', '全职运营']
  },
  {
    id: '4',
    name: 'T3级超级合伙人',
    deposit: 50000,
    commission: '15%（持续）+ 年度奖励',
    features: ['50000元押金', '持续性分成', '团队化运作']
  }
])

// 获取合伙人信息
const loadPartnerInfo = async () => {
  try {
    partnerInfo.value = await getPartnerInfoData()
  } catch (error) {
    console.log('获取合伙人信息失败:', error)
    // 如果获取失败，说明用户不是合伙人，不显示合伙人信息
    partnerInfo.value = null
  }
}

// 获取合伙人等级名称
const getPartnerLevelName = (levelId: string) => {
  const level = partnerLevels.value.find(l => l.id === levelId)
  return level ? level.name : '未知等级'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取押金状态文本
const getDepositStatusText = (status: string) => {
  const statusMap = {
    '0': '未缴纳',
    '1': '部分缴纳',
    '2': '完全缴纳',
    '3': '已退还'
  }
  return statusMap[status] || '未知状态'
}

// 获取押金状态样式类
const getDepositStatusClass = (status: string) => {
  const classMap = {
    '0': 'text-red-600',
    '1': 'text-yellow-600',
    '2': 'text-green-600',
    '3': 'text-gray-600'
  }
  return classMap[status] || 'text-gray-600'
}

const loadData = async () => {
  swiperDatas.value = await getBannerListData()
  categoryDatas.value = await getCategoryListData()
  goodsLimitDatas.value = await getRecommendedListData("limit")
  goodsHotDatas.value = await getRecommendedListData("hot")
  await loadPartnerInfo()
}

const toCategory = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods-list?category_id=${id}`
  })
}
const toGoods = (id) => {
  uni.navigateTo({
    url: `/pages/goods/goods?id=${id}`
  })
}
const topage = (item) => {
  // 处理轮播图点击事件，可以根据item的属性进行不同的跳转
  if (item.url) {
    uni.navigateTo({
      url: item.url
    })
  }
}

onPageScroll(e => {
  useTop.value.change(e.scrollTop);
})

onShow(() => {
  loadData()
  share()
})

onPullDownRefresh(async () => {
  await loadData()
  uni.stopPullDownRefresh()
})

onLoad(() => {
})
</script>

<style lang="scss">

/* 合伙人信息区 */
.partner-info-area {
  margin-top: 20rpx;

  .grid {
    display: grid;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .gap-4 {
    gap: 1rem;
  }

  .bg-gray-50 {
    background-color: #f9fafb;
  }

  .p-3 {
    padding: 0.75rem;
  }

  .rounded-lg {
    border-radius: 0.5rem;
  }

  .text-gray-600 {
    color: #4b5563;
  }

  .text-gray-800 {
    color: #1f2937;
  }

  .font-medium {
    font-weight: 500;
  }

  .block {
    display: block;
  }

  .mb-1 {
    margin-bottom: 0.25rem;
  }
}

/* 轮播图区 */
.swiper-area {
  .swiper {
    height: 240rpx;
  }
}

/* 分类区 */
.category-area {
  padding: 60rpx 0 30rpx 0;

  .category-item {
    font-size: $font-sm + 2upx;
    color: $font-color-dark;
    width: 25%;
  }

  image {
    width: 96rpx;
    height: 96rpx;
  }
}

/* 限时精选区 */
.limit-area {
  min-height: 240rpx;

  .item {
    width: 240rpx;

    image {
      width: 240rpx;
      height: 240rpx;
    }
  }
}

</style>
